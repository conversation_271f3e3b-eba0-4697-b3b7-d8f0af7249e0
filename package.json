{"name": "scholarify-admin", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build && pm2 restart scholarify-admin", "start": "next start -p 3001", "lint": "next lint"}, "dependencies": {"@internationalized/date": "^3.7.0", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-hover-card": "^1.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-navigation-menu": "^1.2.5", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-tooltip": "^1.1.8", "@react-aria/datepicker": "^3.14.1", "@react-pdf/renderer": "^4.3.0", "@react-stately/datepicker": "^3.13.0", "@remixicon/react": "^4.6.0", "@tailwindcss/postcss": "^4.0.17", "bcrypt": "^5.1.1", "daisyui": "^5.0.9", "date-fns": "^3.6.0", "framer-motion": "^12.6.2", "js-cookie": "^3.0.5", "jwt-decode": "^4.0.0", "lottie-react": "^2.4.1", "lucide-react": "^0.485.0", "next": "^15.3.3", "react": "^18", "react-day-picker": "^8.10.1", "react-dom": "^18", "react-select": "^5.10.1", "react-toggle-dark-mode": "^1.1.1", "recharts": "^2.15.2", "sharp": "^0.33.5", "tailwind-merge": "^3.0.2"}, "devDependencies": {"@tailwindcss/forms": "^0.5.10", "@types/bcrypt": "^5.0.2", "@types/js-cookie": "^3.0.6", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "postcss": "^8", "tailwind-scrollbar": "^3.1.0", "tailwindcss": "^3.4.17", "typescript": "^5"}}