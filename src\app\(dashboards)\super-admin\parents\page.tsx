"use client";

import { motion } from 'framer-motion';
import SuperLayout from '@/components/Dashboard/Layouts/SuperLayout';
import CircularLoader from '@/components/widgets/CircularLoader';
import React, { Suspense, useEffect, useState, useMemo, useRef, useCallback } from 'react';
import { UserPlus, Search } from 'lucide-react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import useAuth from '@/app/hooks/useAuth';
import { getSchools } from '@/app/services/SchoolServices';
import { getStudents } from '@/app/services/StudentServices';
import { SchoolSchema } from '@/app/models/SchoolModel';
import { StudentSchema } from '@/app/models/StudentModel';
import CreateInvitationModal from './components/CreateInviteModal';
import NotificationCard from '@/components/NotificationCard';
import DeleteInviteModal from './components/DeleteInviteModal';
import { deleteUser, getParents, getParentsByPage, registerParent, verifyPassword } from '@/app/services/UserServices';
import { UserSchema } from '@/app/models/UserModel';
import ActionButton from '@/components/ActionButton';

// New Component: ParentCard (remains mostly the same)
interface ParentCardProps {
    parent: UserSchema;
    onDelete: (parent: UserSchema) => void;
    onView: (parent: UserSchema) => void;
    getSchoolNames: (schoolIds?: string[]) => string;
    getStudentNames: (studentIds?: string[]) => string;
}

const ParentCard: React.FC<ParentCardProps> = React.memo(({ parent, onDelete, onView, getSchoolNames, getStudentNames }) => {
    const fallbackAvatar = "https://ui-avatars.com/api/?name=" + encodeURIComponent(parent.name || parent.email) + "&background=random&color=fff";

    return (
        <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
            className="bg-widget dark:border dark:border-gray-800 text-foreground p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200 flex flex-col items-center text-center"
        >
            <div className="mb-4">
                <img
                    src={parent.avatar || fallbackAvatar}
                    alt={parent.name || "Parent Avatar"}
                    className="w-24 h-24 rounded-full object-cover border-2 border-gray-200"
                    onError={(e) => {
                        e.currentTarget.src = fallbackAvatar;
                    }}
                />
            </div>
            <h3 className="text-xl font-semibold text-foreground mb-2">{parent.name || "N/A"}</h3>
            <p className="text-gray-400 mb-1"><strong>Email:</strong> {parent.email}</p>
            <p className="text-gray-400 mb-1"><strong>Phone:</strong> {parent.phone || "N/A"}</p>
            <p className="text-gray-400 mb-1"><strong>Address:</strong> {parent.address || "N/A"}</p>
            <p className="text-gray-400 mb-1"><strong>Schools:</strong> {getSchoolNames(parent.school_ids)}</p>
            <p className="text-gray-400 mb-4"><strong>Children:</strong> {getStudentNames(parent.student_ids)}</p>

            <div className="flex gap-3 mt-auto">
                <ActionButton
                    action="view"
                    label="View"
                    onClick={() => onView(parent)}
                />
                <ActionButton
                    action="delete"
                    label="Delete"
                    onClick={() => onDelete(parent)}
                />
            </div>
        </motion.div>
    );
});
ParentCard.displayName = 'ParentCard';

// New Component: ParentCardSkeleton
const ParentCardSkeleton: React.FC = () => (
    <div className="bg-widget dark:border dark:border-gray-800 p-6 rounded-lg shadow-md flex flex-col items-center text-center animate-pulse">
        <div className="mb-4 bg-gray-300 dark:bg-gray-700 rounded-full w-24 h-24"></div>
        <div className="h-6 bg-gray-300 dark:bg-gray-700 rounded w-3/4 mb-2"></div>
        <div className="h-4 bg-gray-300 dark:bg-gray-700 rounded w-full mb-1"></div>
        <div className="h-4 bg-gray-300 dark:bg-gray-700 rounded w-5/6 mb-1"></div>
        <div className="h-4 bg-gray-300 dark:bg-gray-700 rounded w-2/3 mb-1"></div>
        <div className="h-4 bg-gray-300 dark:bg-gray-700 rounded w-full mb-4"></div>
        <div className="h-4 bg-gray-300 dark:bg-gray-700 rounded w-5/6 mb-4"></div>
        <div className="flex gap-3 mt-auto w-full justify-center">
            <div className="h-10 bg-gray-300 dark:bg-gray-700 rounded w-20"></div>
            <div className="h-10 bg-gray-300 dark:bg-gray-700 rounded w-20"></div>
        </div>
    </div>
);

export default function Page() {
    const BASE_URL = "/super-admin";

    const navigation = {
        icon: UserPlus,
        baseHref: `${BASE_URL}/parents`,
        title: "Manage Parent Invitations",
    };

    function Parents() {
        const router = useRouter();
        const [parents, setParents] = useState<UserSchema[]>([]);
        const [schools, setSchools] = useState<SchoolSchema[]>([]);
        const [students, setStudents] = useState<StudentSchema[]>([]);
        const [isModalOpen, setIsModalOpen] = useState(false);
        const [loadingData, setLoadingData] = useState(true);
        const [isNotificationCard, setIsNotificationCard] = useState(false);
        const [notificationMessage, setNotificationMessage] = useState("");
        const [notificationType, setNotificationType] = useState<"success" | "error" | "info" | "warning">("success");
        const [isSubmitting, setIsSubmitting] = useState(false);
        const [submitStatus, setSubmitStatus] = useState<"success" | "failure" | null>(null);
        const [invitationToDelete, setinvitationToDelete] = useState<UserSchema | null>(null);

        // --- Infinite Scrolling States ---
        const [currentPage, setCurrentPage] = useState(1);
        const [hasMore, setHasMore] = useState(true);
        const [isFetchingMore, setIsFetchingMore] = useState(false);
        const PARENTS_PER_PAGE = 12;
        const loadMoreRef = useRef<HTMLDivElement>(null);

        // --- Search States ---
        const [searchTerm, setSearchTerm] = useState("");
        const [debouncedSearchTerm, setDebouncedSearchTerm] = useState("");

        const { user } = useAuth();

        // Debounce effect for search term
        useEffect(() => {
            const handler = setTimeout(() => {
                setDebouncedSearchTerm(searchTerm);
            }, 300);

            return () => {
                clearTimeout(handler);
            };
        }, [searchTerm]);

        // Effect to refetch parents when debouncedSearchTerm changes
        useEffect(() => {
            // When search term changes, reset pagination and fetch first page
            setParents([]);
            setCurrentPage(1);
            setHasMore(true);
            if (debouncedSearchTerm !== undefined) {
                fetchInitialData(1, debouncedSearchTerm);
            }
        }, [debouncedSearchTerm]);


        // useCallback to memoize functions passed to children (optimization)
        const getSchoolNames = useCallback((schoolIds?: string[]) => {
            if (!schoolIds || schoolIds.length === 0) return "No Schools";
            return schoolIds.map(id => {
                const school = schools.find(s => s._id === id);
                return school ? school.name : "Unknown School";
            }).join(", ");
        }, [schools]);

        const getStudentNames = useCallback((studentIds?: string[]) => {
            if (!studentIds || studentIds.length === 0) return "No Children";
            return studentIds.map(id => {
                const student = students.find(s => s._id === id);
                return student ? student.name : "Unknown Student";
            }).join(", ");
        }, [students]);

        // --- Main Data Fetching Function for Parents and related data ---
        const fetchInitialData = async (pageToFetch: number, search: string = "") => {
            setLoadingData(true);
            try {
                const [
                    fetchedSchools,
                    fetchedStudents,
                    paginatedParentsResponse
                ] = await Promise.all([
                    getSchools(),
                    getStudents(),
                    getParentsByPage(pageToFetch, PARENTS_PER_PAGE)
                ]);

                setSchools(fetchedSchools);
                setStudents(fetchedStudents);
                setParents(paginatedParentsResponse.parents);
                setHasMore(paginatedParentsResponse.hasMore);
                setCurrentPage(paginatedParentsResponse.currentPage);

            } catch (error) {
                console.error("Error fetching initial data:", error);
                setNotificationMessage("Failed to load data. Please try again.");
                setNotificationType("error");
                setIsNotificationCard(true);
                setHasMore(false);
            } finally {
                setLoadingData(false);
            }
        };

        // --- Fetch More Parents Function for Infinite Scrolling ---
        const fetchMoreParents = async () => {
            if (isFetchingMore || !hasMore || loadingData) return;

            setIsFetchingMore(true);
            try {
                const nextPage = currentPage + 1;
                const paginatedParentsResponse = await getParentsByPage(nextPage, PARENTS_PER_PAGE);

                setParents(prevParents => [...prevParents, ...paginatedParentsResponse.parents]);
                setHasMore(paginatedParentsResponse.hasMore);
                setCurrentPage(nextPage);

            } catch (error) {
                console.error("Error fetching more parents:", error);
                setNotificationMessage("Failed to load more parents. Please try again.");
                setNotificationType("error");
                setIsNotificationCard(true);
                setHasMore(false);
            } finally {
                setIsFetchingMore(false);
            }
        };


        // Initial data load on component mount
        useEffect(() => {
            fetchInitialData(1, debouncedSearchTerm);
        }, []);

        // Intersection Observer for infinite scrolling
        useEffect(() => {
            if (!loadMoreRef.current || !hasMore || isFetchingMore || loadingData) return;

            const observer = new IntersectionObserver(
                (entries) => {
                    if (entries[0].isIntersecting && hasMore && !isFetchingMore && !loadingData) {
                        fetchMoreParents();
                    }
                },
                {
                    rootMargin: "200px",
                    threshold: 0.1,
                }
            );

            observer.observe(loadMoreRef.current);

            return () => {
                if (loadMoreRef.current) {
                    observer.unobserve(loadMoreRef.current);
                }
            };
        }, [hasMore, isFetchingMore, loadingData, fetchMoreParents]);


        const handleSaveInvitation = async (invitationData: UserSchema) => {
            setIsSubmitting(true);
            setSubmitStatus(null);
            try {
                const newParent: UserSchema = {
                    email: invitationData.email,
                    role: "parent",
                    phone: invitationData.phone,
                    address: invitationData.address,
                    name: invitationData.name,
                    school_ids: invitationData.school_ids,
                    student_ids: invitationData.student_ids,
                    _id: '',
                    user_id: ''
                };
                const data = await registerParent(newParent);
                if (data) {
                    await fetchInitialData(1, debouncedSearchTerm);

                    setSubmitStatus("success");
                    setNotificationMessage("Parent registered successfully!");
                    setNotificationType("success");
                    setIsNotificationCard(true);

                    setTimeout(() => {
                        setIsModalOpen(false);
                        setSubmitStatus(null);
                        setIsNotificationCard(false);
                    }, 3000);
                }
            } catch (error) {
                console.error("Error creating parent:", error);
                setSubmitStatus("failure");
                const errorMessage =
                    error instanceof Error
                        ? error.message
                        : "An unknown error occurred while registering the parent.";
                setNotificationMessage(errorMessage);
                setNotificationType("error");
                setIsNotificationCard(true);
                setTimeout(() => setIsNotificationCard(false), 5000);
            } finally {
                setIsSubmitting(false);
            }
        };

        const handeDeleteInvitation = async (password: string) => {
            setIsSubmitting(true);
            setSubmitStatus(null);

            if (!invitationToDelete || !user) {
                setIsSubmitting(false);
                setNotificationMessage("No parent selected for deletion or user not authenticated.");
                setNotificationType("error");
                setIsNotificationCard(true);
                setTimeout(() => setIsNotificationCard(false), 5000);
                return;
            }

            try {
                const passwordVerified = await verifyPassword(password, user.email);
                if (!passwordVerified) {
                    setNotificationMessage("Invalid Password!");
                    setNotificationType("error");
                    setIsNotificationCard(true);
                    setSubmitStatus("failure");
                    setIsSubmitting(false);
                    setTimeout(() => setIsNotificationCard(false), 5000);
                    return;
                }

                await deleteUser(invitationToDelete.user_id);

                await fetchInitialData(1, debouncedSearchTerm);

                setSubmitStatus("success");
                setNotificationMessage("Parent deleted successfully!");
                setNotificationType("success");
                setIsNotificationCard(true);

                setTimeout(() => {
                    setinvitationToDelete(null);
                    setSubmitStatus(null);
                    setIsNotificationCard(false);
                }, 3000);
            } catch (error) {
                console.error("Error Deleting Parent:", error);
                setSubmitStatus("failure");
                const errorMessage =
                    error instanceof Error
                        ? error.message
                        : "An unknown error occurred while deleting the parent.";
                setNotificationMessage(errorMessage);
                setNotificationType("error");
                setIsNotificationCard(true);
                setTimeout(() => setIsNotificationCard(false), 5000);
            } finally {
                setIsSubmitting(false);
            }
        };

        const filteredParents = useMemo(() => {
            if (!debouncedSearchTerm) {
                return parents;
            }
            const lowercasedSearchTerm = debouncedSearchTerm.toLowerCase();
            return parents.filter(parent =>
                (parent.name?.toLowerCase().includes(lowercasedSearchTerm)) ||
                (parent.email.toLowerCase().includes(lowercasedSearchTerm)) ||
                (parent.phone?.toLowerCase().includes(lowercasedSearchTerm)) ||
                (parent.address?.toLowerCase().includes(lowercasedSearchTerm)) ||
                getSchoolNames(parent.school_ids).toLowerCase().includes(lowercasedSearchTerm) ||
                getStudentNames(parent.student_ids).toLowerCase().includes(lowercasedSearchTerm)
            );
        }, [parents, debouncedSearchTerm, getSchoolNames, getStudentNames]);


        return (
            <div className="">
                {isNotificationCard && (
                    <NotificationCard
                        title="Notification"
                        icon={
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                                <path d="M12 22C17.5 22 22 17.5 22 12C22 6.5 17.5 2 12 2C6.5 2 2 6.5 2 12C2 17.5 6.5 22 12 22Z" stroke="#15803d" strokeWidth="1.5" />
                                <path d="M7.75 11.9999L10.58 14.8299L16.25 9.16992" stroke="#15803d" strokeWidth="1.5" />
                            </svg>
                        }
                        message={notificationMessage}
                        onClose={() => setIsNotificationCard(false)}
                        type={notificationType}
                        isVisible={isNotificationCard}
                        isFixed={true}
                    />
                )}
                <div className="flex flex-col sm:flex-row justify-between items-center mb-6 gap-4">
                    <ActionButton
                        action="add"
                        label="Add Parent"
                        onClick={() => setIsModalOpen(true)}
                    />
                    <div className="relative w-full sm:w-1/2 md:w-1/3">
                        <input
                            type="text"
                            placeholder="Search parents..."
                            className="w-full bg-widget text-foreground pl-10 pr-4 py-2 border border-gray-300 dark:border dark:border-gray-800 rounded-md"
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                        />
                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                    </div>
                </div>

                {isModalOpen && (
                    <CreateInvitationModal
                        onClose={() => { setIsModalOpen(false); setSubmitStatus(null); }}
                        onSave={handleSaveInvitation}
                        submitStatus={submitStatus}
                        isSubmitting={isSubmitting}
                    />
                )}
                {invitationToDelete && (
                    <DeleteInviteModal
                        className={invitationToDelete.name || ""}
                        onClose={() => { setinvitationToDelete(null); setSubmitStatus(null); }}
                        onDelete={handeDeleteInvitation}
                        submitStatus={submitStatus}
                        isSubmitting={isSubmitting}
                    />
                )}

                {loadingData && currentPage === 1 ? (
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                        {Array.from({ length: PARENTS_PER_PAGE }).map((_, index) => (
                            <ParentCardSkeleton key={index} />
                        ))}
                    </div>
                ) : (
                    <>
                        {filteredParents.length > 0 ? (
                            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                                {filteredParents.map((parent) => (
                                    <ParentCard
                                        key={parent._id}
                                        parent={parent}
                                        onDelete={setinvitationToDelete}
                                        onView={(p) => router.push(`${BASE_URL}/parents/view?id=${p._id}`)}
                                        getSchoolNames={getSchoolNames}
                                        getStudentNames={getStudentNames}
                                    />
                                ))}
                            </div>
                        ) : (
                            <p className="col-span-full text-center text-gray-500 text-lg">No parents found.</p>
                        )}

                        {hasMore && (
                            <div ref={loadMoreRef} className="flex justify-center items-center py-4 mt-6">
                                {isFetchingMore ? ( // Show CircularLoader only when fetching more, not during initial load
                                    <CircularLoader size={24} color="teal" />
                                ) : (
                                    // Optionally, you can have a "Load More" button here if you prefer explicit loading over infinite scroll
                                    // Or simply keep it empty as the IntersectionObserver handles it
                                    <p className="text-gray-500">Loading more parents...</p>
                                )}
                            </div>
                        )}

                        {!hasMore && parents.length > 0 && !isFetchingMore && (
                            <p className="col-span-full text-center text-gray-500 mt-4">You've reached the end of the list!</p>
                        )}
                    </>
                )}
            </div>
        );
    }

    return (
        <Suspense fallback={
            <div className="flex justify-center items-center h-screen absolute top-0 left-0 z-50 w-full bg-background">
                <CircularLoader size={32} color="teal" />
            </div>
        }>
            <SuperLayout
                navigation={navigation}
                showGoPro={true}
                onLogout={() => console.log("Logged out")}
            >
                <Parents />
            </SuperLayout>
        </Suspense>
    );
}