const ExamPeriod = require('../models/ExamPeriod');
const ClassSchedule = require('../models/ClassSchedule');
const NotificationService = require('./notificationService');

class ExamPeriodService {
  
  /**
   * Activate an exam period and handle normal class suspension
   */
  static async activateExamPeriod(examPeriodId, userId) {
    try {
      const examPeriod = await ExamPeriod.findById(examPeriodId)
        .populate('term_id', 'name term_type')
        .populate('school_id', 'name');

      if (!examPeriod) {
        throw new Error('Exam period not found');
      }

      if (examPeriod.status === 'active') {
        return { message: 'Exam period is already active', examPeriod };
      }

      // Activate the exam period
      await examPeriod.activate();

      // If suspend_normal_classes is enabled, suspend conflicting normal classes
      if (examPeriod.settings.suspend_normal_classes) {
        const suspendedCount = await this.suspendConflictingNormalClasses(examPeriod);
        console.log(`📝 Suspended ${suspendedCount} normal classes for exam period: ${examPeriod.name}`);
      }

      // Send notifications if enabled
      if (examPeriod.settings.notify_teachers) {
        await this.notifyTeachersAboutExamPeriod(examPeriod, 'activated');
      }

      return {
        message: 'Exam period activated successfully',
        examPeriod,
        suspendedClasses: examPeriod.settings.suspend_normal_classes
      };
    } catch (error) {
      console.error('Error activating exam period:', error);
      throw error;
    }
  }

  /**
   * Complete an exam period and reactivate suspended normal classes
   */
  static async completeExamPeriod(examPeriodId, userId) {
    try {
      const examPeriod = await ExamPeriod.findById(examPeriodId)
        .populate('term_id', 'name term_type')
        .populate('school_id', 'name');

      if (!examPeriod) {
        throw new Error('Exam period not found');
      }

      if (examPeriod.status === 'completed') {
        return { message: 'Exam period is already completed', examPeriod };
      }

      // Complete the exam period
      await examPeriod.complete();

      // Reactivate suspended normal classes
      if (examPeriod.settings.suspend_normal_classes) {
        const reactivatedCount = await ClassSchedule.reactivateNormalClasses(
          examPeriod.school_id, 
          examPeriodId
        );
        console.log(`🔄 Reactivated ${reactivatedCount.modifiedCount} normal classes after exam period: ${examPeriod.name}`);
      }

      // Send notifications if enabled
      if (examPeriod.settings.notify_teachers) {
        await this.notifyTeachersAboutExamPeriod(examPeriod, 'completed');
      }

      return {
        message: 'Exam period completed successfully',
        examPeriod,
        reactivatedClasses: examPeriod.settings.suspend_normal_classes
      };
    } catch (error) {
      console.error('Error completing exam period:', error);
      throw error;
    }
  }

  /**
   * Suspend normal classes that conflict with exam schedules
   */
  static async suspendConflictingNormalClasses(examPeriod) {
    try {
      // Get all exam schedules for this exam period
      const examSchedules = await ClassSchedule.find({
        exam_period_id: examPeriod._id,
        schedule_type: 'Exam',
        status: 'active'
      });

      let suspendedCount = 0;

      // For each exam schedule, suspend conflicting normal classes
      for (const examSchedule of examSchedules) {
        const conflictingNormalClasses = await ClassSchedule.find({
          school_id: examPeriod.school_id,
          class_id: examSchedule.class_id,
          period_id: examSchedule.period_id,
          day_of_week: examSchedule.day_of_week,
          schedule_type: 'Normal',
          status: 'active'
        });

        for (const normalClass of conflictingNormalClasses) {
          await ClassSchedule.findByIdAndUpdate(normalClass._id, {
            status: 'suspended',
            notes: `Suspended due to exam period: ${examPeriod.name} (${examPeriod._id})`
          });
          suspendedCount++;
        }
      }

      return suspendedCount;
    } catch (error) {
      console.error('Error suspending conflicting normal classes:', error);
      throw error;
    }
  }

  /**
   * Check if a date falls within any active exam period
   */
  static async getActiveExamPeriodForDate(schoolId, date, termId = null) {
    try {
      return await ExamPeriod.getActiveExamPeriod(schoolId, date, termId);
    } catch (error) {
      console.error('Error checking active exam period:', error);
      throw error;
    }
  }

  /**
   * Get exam periods that conflict with a date range
   */
  static async getConflictingExamPeriods(schoolId, startDate, endDate, excludeId = null) {
    try {
      return await ExamPeriod.checkConflicts(schoolId, startDate, endDate, excludeId);
    } catch (error) {
      console.error('Error checking exam period conflicts:', error);
      throw error;
    }
  }

  /**
   * Send notifications to teachers about exam period status changes
   */
  static async notifyTeachersAboutExamPeriod(examPeriod, action) {
    try {
      // Get all teachers who have exam schedules in this period
      const examSchedules = await ClassSchedule.find({
        exam_period_id: examPeriod._id,
        schedule_type: 'Exam'
      }).populate('teacher_id', '_id first_name last_name name');

      const teacherIds = [...new Set(examSchedules.map(schedule => schedule.teacher_id?._id).filter(Boolean))];

      if (teacherIds.length === 0) {
        console.log('📧 No teachers to notify for exam period:', examPeriod.name);
        return;
      }

      const notificationTitle = action === 'activated' 
        ? `Exam Period Started: ${examPeriod.name} 📝`
        : `Exam Period Completed: ${examPeriod.name} ✅`;

      const notificationMessage = action === 'activated'
        ? `The exam period "${examPeriod.name}" has started. Please check your exam supervision schedule.`
        : `The exam period "${examPeriod.name}" has been completed. Normal classes will resume.`;

      // Send notifications to all involved teachers
      for (const teacherId of teacherIds) {
        try {
          await NotificationService.createNotification({
            recipient_id: teacherId,
            school_id: examPeriod.school_id,
            type: 'info',
            category: 'schedule',
            title: notificationTitle,
            message: notificationMessage,
            sender_type: 'system',
            related_entity: {
              entity_type: 'schedule',
              entity_id: examPeriod._id
            },
            action_url: '/teacher-dashboard/supervisions',
            action_label: 'View Supervisions',
            priority: 'high',
            channels: {
              in_app: true,
              email: true
            },
            metadata: {
              exam_period: {
                name: examPeriod.name,
                exam_type: examPeriod.exam_type,
                start_date: examPeriod.start_date,
                end_date: examPeriod.end_date,
                action: action
              }
            }
          });
        } catch (notificationError) {
          console.error(`Error sending notification to teacher ${teacherId}:`, notificationError);
        }
      }

      console.log(`📧 Sent ${action} notifications to ${teacherIds.length} teachers for exam period: ${examPeriod.name}`);
    } catch (error) {
      console.error('Error sending exam period notifications:', error);
      throw error;
    }
  }

  /**
   * Auto-activate exam periods that should be active based on current date
   */
  static async autoActivateExamPeriods() {
    try {
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      const examPeriodsToActivate = await ExamPeriod.find({
        status: 'scheduled',
        start_date: { $lte: today },
        end_date: { $gte: today }
      });

      console.log(`🔄 Found ${examPeriodsToActivate.length} exam periods to auto-activate`);

      for (const examPeriod of examPeriodsToActivate) {
        try {
          await this.activateExamPeriod(examPeriod._id, null);
          console.log(`✅ Auto-activated exam period: ${examPeriod.name}`);
        } catch (error) {
          console.error(`❌ Failed to auto-activate exam period ${examPeriod.name}:`, error);
        }
      }

      return examPeriodsToActivate.length;
    } catch (error) {
      console.error('Error in auto-activate exam periods:', error);
      throw error;
    }
  }

  /**
   * Auto-complete exam periods that have ended
   */
  static async autoCompleteExamPeriods() {
    try {
      const today = new Date();
      today.setHours(23, 59, 59, 999);

      const examPeriodsToComplete = await ExamPeriod.find({
        status: 'active',
        end_date: { $lt: today }
      });

      console.log(`🔄 Found ${examPeriodsToComplete.length} exam periods to auto-complete`);

      for (const examPeriod of examPeriodsToComplete) {
        try {
          await this.completeExamPeriod(examPeriod._id, null);
          console.log(`✅ Auto-completed exam period: ${examPeriod.name}`);
        } catch (error) {
          console.error(`❌ Failed to auto-complete exam period ${examPeriod.name}:`, error);
        }
      }

      return examPeriodsToComplete.length;
    } catch (error) {
      console.error('Error in auto-complete exam periods:', error);
      throw error;
    }
  }
}

module.exports = ExamPeriodService;
