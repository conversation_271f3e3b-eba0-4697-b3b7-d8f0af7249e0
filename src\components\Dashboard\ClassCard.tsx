"use client";

import React from "react";
import { motion } from "framer-motion";
import { 
  Users, 
  Calendar, 
  Clock, 
  BookOpen, 
  CheckCircle, 
  XCircle, 
  UserCheck,
  GraduationCap,
  ChevronRight
} from "lucide-react";

interface ClassStats {
  totalSlots: number;
  occupiedSlots: number;
  freeSlots: number;
  assignedTeachers: number;
  totalSubjects: number;
  examSlots?: number;
}

interface ClassCardProps {
  classData: {
    _id: string;
    name: string;
    grade_level?: string;
    student_count?: number;
    section?: string;
  };
  stats: ClassStats;
  isExamMode?: boolean;
  onClick: () => void;
  index: number;
}

export default function ClassCard({ 
  classData, 
  stats, 
  isExamMode = false, 
  onClick, 
  index 
}: ClassCardProps) {
  
  const occupancyPercentage = stats.totalSlots > 0 
    ? Math.round((stats.occupiedSlots / stats.totalSlots) * 100) 
    : 0;

  const getOccupancyColor = (percentage: number) => {
    if (percentage >= 80) return "text-green-600 dark:text-green-400";
    if (percentage >= 50) return "text-yellow-600 dark:text-yellow-400";
    return "text-red-600 dark:text-red-400";
  };

  const getOccupancyBgColor = (percentage: number) => {
    if (percentage >= 80) return "bg-green-100 dark:bg-green-900/20";
    if (percentage >= 50) return "bg-yellow-100 dark:bg-yellow-900/20";
    return "bg-red-100 dark:bg-red-900/20";
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: index * 0.1, duration: 0.3 }}
      whileHover={{ scale: 1.02, y: -2 }}
      whileTap={{ scale: 0.98 }}
      onClick={onClick}
      className="bg-widget rounded-xl border border-stroke p-6 cursor-pointer hover:shadow-lg transition-all duration-300 group"
    >
      {/* Header */}
      <div className="flex items-start justify-between mb-4">
        <div className="flex-1">
          <div className="flex items-center gap-2 mb-1">
            <div className="w-8 h-8 bg-teal/10 rounded-lg flex items-center justify-center">
              <GraduationCap className="h-4 w-4 text-teal" />
            </div>
            <h3 className="font-semibold text-foreground text-lg group-hover:text-teal transition-colors">
              {classData.name}
            </h3>
          </div>
          
          <div className="flex items-center gap-3 text-sm text-foreground/60">
            {classData.grade_level && (
              <span className="flex items-center gap-1">
                <BookOpen className="h-3 w-3" />
                Grade {classData.grade_level}
              </span>
            )}
            {classData.student_count && (
              <span className="flex items-center gap-1">
                <Users className="h-3 w-3" />
                {classData.student_count} students
              </span>
            )}
            {classData.section && (
              <span className="px-2 py-1 bg-gray-100 dark:bg-gray-800 rounded text-xs">
                Section {classData.section}
              </span>
            )}
          </div>
        </div>
        
        <ChevronRight className="h-5 w-5 text-foreground/40 group-hover:text-teal group-hover:translate-x-1 transition-all" />
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-2 gap-4 mb-4">
        {/* Occupancy Rate */}
        <div className={`p-3 rounded-lg ${getOccupancyBgColor(occupancyPercentage)}`}>
          <div className="flex items-center justify-between mb-1">
            <span className="text-xs font-medium text-foreground/70">
              {isExamMode ? 'Exam Coverage' : 'Schedule Coverage'}
            </span>
            <span className={`text-sm font-bold ${getOccupancyColor(occupancyPercentage)}`}>
              {occupancyPercentage}%
            </span>
          </div>
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div 
              className={`h-2 rounded-full transition-all duration-500 ${
                occupancyPercentage >= 80 
                  ? 'bg-green-500' 
                  : occupancyPercentage >= 50 
                  ? 'bg-yellow-500' 
                  : 'bg-red-500'
              }`}
              style={{ width: `${occupancyPercentage}%` }}
            />
          </div>
        </div>

        {/* Assigned Teachers */}
        <div className="p-3 rounded-lg bg-blue-50 dark:bg-blue-900/20">
          <div className="flex items-center justify-between mb-1">
            <span className="text-xs font-medium text-foreground/70">Teachers</span>
            <UserCheck className="h-4 w-4 text-blue-600 dark:text-blue-400" />
          </div>
          <div className="text-lg font-bold text-blue-600 dark:text-blue-400">
            {stats.assignedTeachers}
          </div>
          <div className="text-xs text-foreground/60">
            {stats.totalSubjects} subjects
          </div>
        </div>
      </div>

      {/* Time Slots */}
      <div className="grid grid-cols-3 gap-2">
        {/* Total Slots */}
        <div className="text-center p-2 bg-gray-50 dark:bg-gray-800/50 rounded-lg">
          <div className="flex items-center justify-center mb-1">
            <Calendar className="h-3 w-3 text-foreground/60" />
          </div>
          <div className="text-sm font-semibold text-foreground">
            {stats.totalSlots}
          </div>
          <div className="text-xs text-foreground/60">Total</div>
        </div>

        {/* Occupied Slots */}
        <div className="text-center p-2 bg-green-50 dark:bg-green-900/20 rounded-lg">
          <div className="flex items-center justify-center mb-1">
            <CheckCircle className="h-3 w-3 text-green-600 dark:text-green-400" />
          </div>
          <div className="text-sm font-semibold text-green-600 dark:text-green-400">
            {stats.occupiedSlots}
          </div>
          <div className="text-xs text-foreground/60">
            {isExamMode ? 'Exams' : 'Occupied'}
          </div>
        </div>

        {/* Free Slots */}
        <div className="text-center p-2 bg-orange-50 dark:bg-orange-900/20 rounded-lg">
          <div className="flex items-center justify-center mb-1">
            <Clock className="h-3 w-3 text-orange-600 dark:text-orange-400" />
          </div>
          <div className="text-sm font-semibold text-orange-600 dark:text-orange-400">
            {stats.freeSlots}
          </div>
          <div className="text-xs text-foreground/60">Free</div>
        </div>
      </div>

      {/* Exam Mode Indicator */}
      {isExamMode && stats.examSlots !== undefined && (
        <div className="mt-3 pt-3 border-t border-stroke">
          <div className="flex items-center justify-between text-sm">
            <span className="text-foreground/70">Exam Schedules:</span>
            <span className="font-medium text-orange-600 dark:text-orange-400">
              {stats.examSlots} scheduled
            </span>
          </div>
        </div>
      )}

      {/* Hover Effect Indicator */}
      <div className="mt-3 pt-3 border-t border-stroke opacity-0 group-hover:opacity-100 transition-opacity">
        <div className="flex items-center justify-center text-xs text-teal font-medium">
          <span>Click to view {isExamMode ? 'exam' : 'class'} schedule</span>
        </div>
      </div>
    </motion.div>
  );
}
