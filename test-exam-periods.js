// Test script for ExamPeriod functionality
const mongoose = require('mongoose');
require('dotenv').config();

// Import models
const ExamPeriod = require('./src/models/ExamPeriod');
const ClassSchedule = require('./src/models/ClassSchedule');
const Term = require('./src/models/Term');

async function connectDB() {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/scholarify-test');
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ MongoDB connection failed:', error);
    process.exit(1);
  }
}

async function testExamPeriodModel() {
  console.log('\n🧪 Testing ExamPeriod Model...');
  
  try {
    // Test 1: Create a sample exam period
    const sampleExamPeriod = {
      school_id: new mongoose.Types.ObjectId(),
      term_id: new mongoose.Types.ObjectId(),
      name: 'Mid-Term Exams 2024',
      description: 'First semester mid-term examinations',
      start_date: new Date('2024-03-15'),
      end_date: new Date('2024-03-22'),
      exam_type: 'mid_term',
      academic_year: '2023-2024',
      created_by: new mongoose.Types.ObjectId()
    };

    console.log('📝 Creating sample exam period...');
    const examPeriod = new ExamPeriod(sampleExamPeriod);
    
    // Test validation
    const validationError = examPeriod.validateSync();
    if (validationError) {
      console.log('❌ Validation failed:', validationError.message);
      return false;
    }
    
    console.log('✅ ExamPeriod validation passed');
    console.log('📋 Generated exam_period_id:', examPeriod.exam_period_id || 'Will be generated on save');
    
    // Test static methods exist
    if (typeof ExamPeriod.getActiveExamPeriod === 'function') {
      console.log('✅ getActiveExamPeriod static method exists');
    } else {
      console.log('❌ getActiveExamPeriod static method missing');
    }
    
    if (typeof ExamPeriod.checkConflicts === 'function') {
      console.log('✅ checkConflicts static method exists');
    } else {
      console.log('❌ checkConflicts static method missing');
    }
    
    return true;
  } catch (error) {
    console.error('❌ ExamPeriod model test failed:', error.message);
    return false;
  }
}

async function testClassScheduleModel() {
  console.log('\n🧪 Testing ClassSchedule Model Updates...');
  
  try {
    // Test 1: Create a normal schedule
    const normalSchedule = {
      school_id: new mongoose.Types.ObjectId(),
      class_id: new mongoose.Types.ObjectId(),
      subject_id: new mongoose.Types.ObjectId(),
      teacher_id: new mongoose.Types.ObjectId(),
      period_id: new mongoose.Types.ObjectId(),
      day_of_week: 'Monday',
      schedule_type: 'Normal',
      academic_year: '2023-2024'
    };

    console.log('📝 Creating normal schedule...');
    const schedule1 = new ClassSchedule(normalSchedule);
    
    const validation1 = schedule1.validateSync();
    if (validation1) {
      console.log('❌ Normal schedule validation failed:', validation1.message);
      return false;
    }
    
    console.log('✅ Normal schedule validation passed');
    console.log('📊 Priority:', schedule1.priority); // Should be 50
    
    // Test 2: Create an exam schedule
    const examSchedule = {
      school_id: new mongoose.Types.ObjectId(),
      class_id: new mongoose.Types.ObjectId(),
      subject_id: new mongoose.Types.ObjectId(),
      teacher_id: new mongoose.Types.ObjectId(),
      period_id: new mongoose.Types.ObjectId(),
      day_of_week: 'Tuesday',
      schedule_type: 'Exam',
      exam_period_id: new mongoose.Types.ObjectId(),
      academic_year: '2023-2024'
    };

    console.log('📝 Creating exam schedule...');
    const schedule2 = new ClassSchedule(examSchedule);
    
    const validation2 = schedule2.validateSync();
    if (validation2) {
      console.log('❌ Exam schedule validation failed:', validation2.message);
      return false;
    }
    
    console.log('✅ Exam schedule validation passed');
    console.log('📊 Priority:', schedule2.priority); // Should be 100
    
    // Test 3: Test exam schedule without exam_period_id (should fail)
    const invalidExamSchedule = {
      ...examSchedule,
      exam_period_id: undefined
    };
    
    console.log('📝 Testing invalid exam schedule (no exam_period_id)...');
    const schedule3 = new ClassSchedule(invalidExamSchedule);
    
    // This should fail validation in pre-save hook
    try {
      await schedule3.validate();
      console.log('❌ Invalid exam schedule should have failed validation');
      return false;
    } catch (error) {
      console.log('✅ Invalid exam schedule correctly failed validation');
    }
    
    // Test static methods
    if (typeof ClassSchedule.checkConflictWithPriority === 'function') {
      console.log('✅ checkConflictWithPriority static method exists');
    } else {
      console.log('❌ checkConflictWithPriority static method missing');
    }
    
    return true;
  } catch (error) {
    console.error('❌ ClassSchedule model test failed:', error.message);
    return false;
  }
}

async function testPriorityLogic() {
  console.log('\n🧪 Testing Priority Logic...');
  
  try {
    // Test priority assignment
    const normalPriority = 50;
    const examPriority = 100;
    
    console.log('📊 Normal schedule priority:', normalPriority);
    console.log('📊 Exam schedule priority:', examPriority);
    
    if (examPriority > normalPriority) {
      console.log('✅ Exam schedules have higher priority than normal schedules');
    } else {
      console.log('❌ Priority logic is incorrect');
      return false;
    }
    
    // Test conflict resolution logic
    console.log('🔄 Testing conflict resolution...');
    console.log('   - Exam vs Normal: Exam should win (higher priority)');
    console.log('   - Normal vs Exam: Exam should win (higher priority)');
    console.log('   - Exam vs Exam: Conflict (same priority)');
    console.log('   - Normal vs Normal: Conflict (same priority)');
    
    return true;
  } catch (error) {
    console.error('❌ Priority logic test failed:', error.message);
    return false;
  }
}

async function runTests() {
  console.log('🚀 Starting ExamPeriod System Tests...\n');
  
  await connectDB();
  
  const results = {
    examPeriodModel: await testExamPeriodModel(),
    classScheduleModel: await testClassScheduleModel(),
    priorityLogic: await testPriorityLogic()
  };
  
  console.log('\n📊 Test Results Summary:');
  console.log('========================');
  Object.entries(results).forEach(([test, passed]) => {
    console.log(`${passed ? '✅' : '❌'} ${test}: ${passed ? 'PASSED' : 'FAILED'}`);
  });
  
  const allPassed = Object.values(results).every(result => result);
  console.log(`\n🎯 Overall: ${allPassed ? 'ALL TESTS PASSED' : 'SOME TESTS FAILED'}`);
  
  await mongoose.disconnect();
  console.log('\n👋 Disconnected from MongoDB');
  
  process.exit(allPassed ? 0 : 1);
}

// Run tests if this file is executed directly
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = { runTests };
