"use client";

import React, { useState, useEffect } from 'react';
import { Calendar, Clock, BookOpen, Users, AlertCircle, Eye } from 'lucide-react';
import { motion } from 'framer-motion';
import { getTeacherSupervisions, ScheduleEntry } from '@/app/services/TimetableServices';
import { useAcademicYearContext } from '@/context/AcademicYearContext';
import CircularLoader from '@/components/widgets/CircularLoader';

interface ExamSupervisionsProps {
  schoolId: string;
  teacherId: string;
  className?: string;
}

interface SupervisionData {
  supervisions: ScheduleEntry[];
  total: number;
  message: string;
}

export default function ExamSupervisions({ schoolId, teacherId, className = "" }: ExamSupervisionsProps) {
  const [supervisions, setSupervisions] = useState<ScheduleEntry[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { currentAcademicYear } = useAcademicYearContext();

  useEffect(() => {
    fetchSupervisions();
  }, [schoolId, teacherId]);

  const fetchSupervisions = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const data: SupervisionData = await getTeacherSupervisions(schoolId, teacherId, currentAcademicYear);
      setSupervisions(data.supervisions || []);
    } catch (err) {
      console.error('Error fetching supervisions:', err);
      setError('Failed to load exam supervisions');
    } finally {
      setLoading(false);
    }
  };

  const formatTime = (startTime: string, endTime: string) => {
    return `${startTime} - ${endTime}`;
  };

  const getDayColor = (day: string) => {
    const colors: Record<string, string> = {
      'Monday': 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300',
      'Tuesday': 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300',
      'Wednesday': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300',
      'Thursday': 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300',
      'Friday': 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300',
    };
    return colors[day] || 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300';
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'border-l-red-500 bg-red-50 dark:bg-red-900/20';
      case 'urgent':
        return 'border-l-red-600 bg-red-100 dark:bg-red-900/30';
      default:
        return 'border-l-orange-500 bg-orange-50 dark:bg-orange-900/20';
    }
  };

  if (loading) {
    return (
      <div className={`bg-widget rounded-lg border border-stroke p-6 ${className}`}>
        <div className="flex items-center justify-center py-8">
          <CircularLoader />
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`bg-widget rounded-lg border border-stroke p-6 ${className}`}>
        <div className="flex items-center justify-center py-8">
          <div className="text-center">
            <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <p className="text-red-600 font-medium">{error}</p>
            <button
              onClick={fetchSupervisions}
              className="mt-2 text-sm text-blue-600 hover:text-blue-800"
            >
              Try again
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-widget rounded-lg border border-stroke ${className}`}>
      {/* Header */}
      <div className="p-6 border-b border-stroke">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-orange-100 dark:bg-orange-900/30 rounded-full flex items-center justify-center">
              <Eye className="h-5 w-5 text-orange-600 dark:text-orange-400" />
            </div>
            <div>
              <h2 className="text-lg font-semibold text-foreground">Exam Supervisions</h2>
              <p className="text-sm text-foreground/60">
                {supervisions.length} supervision{supervisions.length !== 1 ? 's' : ''} assigned
              </p>
            </div>
          </div>
          
          <button
            onClick={fetchSupervisions}
            className="p-2 text-foreground/60 hover:text-foreground hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md transition-colors"
            title="Refresh"
          >
            <Calendar className="h-4 w-4" />
          </button>
        </div>
      </div>

      {/* Content */}
      <div className="p-6">
        {supervisions.length === 0 ? (
          <div className="text-center py-8">
            <Eye className="h-12 w-12 text-foreground/30 mx-auto mb-4" />
            <p className="text-foreground/60 font-medium">No exam supervisions assigned</p>
            <p className="text-sm text-foreground/50 mt-1">
              You will be notified when exam supervisions are assigned to you
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            {supervisions.map((supervision, index) => (
              <motion.div
                key={supervision._id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className={`border-l-4 rounded-lg p-4 ${getPriorityColor('normal')}`}
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-2">
                      <BookOpen className="h-4 w-4 text-foreground/60" />
                      <span className="font-medium text-foreground">
                        {supervision.subject_name}
                      </span>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDayColor(supervision.day_of_week)}`}>
                        {supervision.day_of_week}
                      </span>
                    </div>
                    
                    <div className="flex items-center space-x-4 text-sm text-foreground/70">
                      <div className="flex items-center space-x-1">
                        <Users className="h-3 w-3" />
                        <span>{supervision.class_name}</span>
                      </div>
                      
                      <div className="flex items-center space-x-1">
                        <Clock className="h-3 w-3" />
                        <span>
                          Period {supervision.period_number} • {formatTime(supervision.start_time, supervision.end_time)}
                        </span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <span className="px-2 py-1 bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300 text-xs font-medium rounded-full">
                      Exam
                    </span>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
