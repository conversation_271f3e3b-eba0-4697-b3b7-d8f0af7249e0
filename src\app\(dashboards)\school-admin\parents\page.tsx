"use client";

import { motion } from 'framer-motion';
// Adjusted relative paths for components and services
import SchoolLayout from '@/components/Dashboard/Layouts/SchoolLayout';
import CircularLoader from '@/components/widgets/CircularLoader';
import React, { Suspense, useEffect, useState } from 'react';
import { UserPlus } from 'lucide-react';
// Mocking Next.js specific hooks for broader environment compatibility if not directly available
import { useRouter, useSearchParams } from 'next/navigation';
import Link from 'next/link';
import useAuth from '@/app/hooks/useAuth'; // Adjusted import path for useAuth hook
import DataTableFix from '@/components/utils/TableFix';
import { getSchoolBy_id, getSchoolById, getSchools } from '../../../services/SchoolServices';
import { createInvitation, deleteInvitation, getInvitations } from '../../../services/InvitationServices';
import { getStudents } from '../../../services/StudentServices'; // Fixed syntax: changed '=>' to 'from'
import { SchoolSchema } from '../../../models/SchoolModel';
import { InvitationCreateSchema, InvitationSchema } from '../../../models/Invitation';
import { StudentSchema } from '../../../models/StudentModel';
import CreateInvitationModal from './components/CreateInvitationModal'; // Relative path for sibling component directory
import NotificationCard from '@/components/NotificationCard';
import DeleteInviteModal from './components/DeleteInvitaitonModal'; // Relative path for sibling component directory
import { deleteUser, getParents, registerParent, verifyPassword } from '../../../services/UserServices';
import { UserSchema } from '../../../models/UserModel';

export default function Page() {

  const { user } = useAuth();
  const schoolId = user?.school_ids?.[0] ?? null;

  const BASE_URL = `/dashboard/school-admin/schools/${schoolId}`; // Adjusted base URL for school admin

  // This navigation object will be passed to the layout.
  // Its title will be dynamically updated based on the fetched school name.
  const navigation = {
    icon: UserPlus,
    baseHref: `${BASE_URL}/parents`,
    title: "Manage Parent Invitations", // This will be updated with school name
  };

  function ParentsComponent() { // Renamed to avoid confusion with the Page component's function
    // Mock useRouter if it's not available in the environment
    const router = useRouter();

    const [parents, setParents] = useState<UserSchema[]>([]);
    const [invitations, setInvitations] = useState<InvitationSchema[]>([]);
    const [schools, setSchools] = useState<SchoolSchema[]>([]);
    const [students, setStudents] = useState<StudentSchema[]>([]);
    const [currentSchool, setCurrentSchool] = useState<SchoolSchema | null>(null); // State for the current school
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [loadingData, setLoadingData] = useState(false);
    const [isNotificationCard, setIsNotificationCard] = useState(false);
    const [notificationMessage, setNotificationMessage] = useState("");
    const [notificationType, setNotificationType] = useState<"success" | "error" | "info" | "warning">("success");
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [submitStatus, setSubmitStatus] = useState<"success" | "failure" | null>(null);
    const [invitationToDelete, setInvitationToDelete] = useState<UserSchema | null>(null); // Renamed for clarity

    // Mock useAuth if it's not available in the environment. Provide a basic mock user.

    useEffect(() => {
      const fetchData = async () => {
        setLoadingData(true);
        try {
          // Fetch current school details
          const fetchedCurrentSchool = await getSchoolBy_id(schoolId as string);
          setCurrentSchool(fetchedCurrentSchool);

          // Update navigation title with school name for display
          if (fetchedCurrentSchool?.name) {
            navigation.title = `Manage Parent Invitations for ${fetchedCurrentSchool.name}`;
          }

          // Fetch all parents, then filter by schoolId and role
          const fetchedParents = await getParents();
          const filteredParents = fetchedParents.filter((parent: { school_ids: any; role: string; }) =>
            (parent.school_ids ?? []).includes(schoolId) && parent.role === "parent"
          );
          setParents(filteredParents);

          // Still fetch all schools and students for utility functions like `getSchoolNames` and `getStudentNames`
          const fetchedSchools = await getSchools();
          const fetchedStudents = await getStudents();

          setSchools(fetchedSchools);
          setStudents(fetchedStudents);

        } catch (error) {
          console.error("Error fetching data:", error);
          setNotificationMessage("Error loading data. Please check network and permissions.");
          setNotificationType("error");
          setIsNotificationCard(true);
        } finally {
          setLoadingData(false);
        }
      };
      fetchData();
    }, [schoolId, navigation]); // Re-fetch if schoolId changes, include navigation to react to title update

    const getSchoolNames = (schoolIds?: string[]) => {
      if (!schoolIds || schoolIds.length === 0) return "No Schools";
      return schoolIds.map(id => {
        const school = schools.find(s => s._id === id);
        return school ? school.name : "Unknown School";
      }).join(", ");
    };

    const getStudentNames = (studentIds?: string[]) => {
      if (!studentIds || studentIds.length === 0) return "No Children";
      return studentIds.map(id => {
        const student = students.find(s => s._id === id);
        // Assuming StudentSchema has an `id` or `_id` property. Use `id` if available, otherwise `_id`.
        return student ? student.name : "Unknown Student";
      }).join(", ");
    };

    const columns = [
      { header: "Email", accessor: (row: UserSchema) => row.email },
      { header: "Phone", accessor: (row: UserSchema) => row.phone || "N/A" },
      { header: "Name", accessor: (row: UserSchema) => row.name || "N/A" },
      { header: "Address", accessor: (row: UserSchema) => row.address || "N/A" },
      { header: "Invited From", accessor: (row: UserSchema) => getSchoolNames(row.school_ids) },
      { header: "Children", accessor: (row: UserSchema) => getStudentNames(row.student_ids) },
    ];

    const actions = [
      {
        label: "View",
        onClick: (user: UserSchema) => {
          // Ensure school_id is passed for viewing a parent
          router.push(`${BASE_URL}/parents/view?id=${user._id}&school_id=${schoolId}`);
        },
      },
      {
        label: "Delete",
        onClick: (userToDelete: UserSchema) => { // Renamed parameter for clarity
          setInvitationToDelete(userToDelete);
        },
      },
    ];

    const handleSaveInvitation = async (invitationData: UserSchema) => {
      setIsSubmitting(true);
      setSubmitStatus(null);
      setLoadingData(true);
      try {
        // Ensure the school_ids array only contains the current schoolId
        const newInvitation: UserSchema = {
          ...invitationData,
          role: "parent",
          school_ids: schoolId ? [schoolId] : [], // Force current schoolId
          _id: '', // Ensure _id is not sent if it's new
          user_id: '' // Ensure user_id is not sent if it's new
        };

        const data = await registerParent(newInvitation);

        if (data) {
          // Re-fetch data to update the table, ensuring filtering by current schoolId
          const fetchedParents = await getParents();
          const filteredParents = fetchedParents.filter((parent: { school_ids: any; role: string; }) =>
            (parent.school_ids ?? []).includes(schoolId!) && parent.role === "parent"
          );
          setParents(filteredParents);

          // Re-fetch schools and students in case they changed (less likely for this use case but good for consistency)
          const fetchedSchools = await getSchools();
          const fetchedStudents = await getStudents();
          setSchools(fetchedSchools);
          setStudents(fetchedStudents);

          setSubmitStatus("success");
          setNotificationMessage("Parent invitation created successfully!");
          setNotificationType("success");
          setIsNotificationCard(true);

          setTimeout(() => {
            setIsModalOpen(false);
            setSubmitStatus(null);
            setIsNotificationCard(false); // Close notification after success
          }, 3000); // Shorter delay for better UX
        }
      } catch (error) {
        console.error("Error creating Parent invitation:", error);
        setSubmitStatus("failure");
        const errorMessage =
          error instanceof Error
            ? error.message
            : "An unknown error occurred while creating the invitation.";
        setNotificationMessage(errorMessage);
        setNotificationType("error");
        setIsNotificationCard(true);
      } finally {
        setIsSubmitting(false);
        setLoadingData(false);
      }
    };

    const handeDeleteInvitation = async (password: string) => {
      setIsSubmitting(true);
      setSubmitStatus(null);
      setLoadingData(true);

      if (!invitationToDelete || !user) {
        console.error("No invitation selected for deletion or user not authenticated.");
        setNotificationMessage("Deletion failed: User not authenticated or no parent selected.");
        setNotificationType("error");
        setIsNotificationCard(true);
        setIsSubmitting(false);
        setLoadingData(false);
        setSubmitStatus("failure");
        setTimeout(() => {
          setInvitationToDelete(null);
          setSubmitStatus(null);
          setIsNotificationCard(false);
        }, 3000);
        return;
      }

      try {
        // Ensure the user object and its email property are not null
        const emailToVerify = user.email || ''; // Fallback to empty string if email is null
        const passwordVerified = await verifyPassword(password, emailToVerify);

        if (!passwordVerified) {
          setNotificationMessage("Invalid Password!");
          setNotificationType("error");
          setIsNotificationCard(true);
          setIsSubmitting(false);
          setLoadingData(false);
          setSubmitStatus("failure");
          setTimeout(() => {
            setInvitationToDelete(null);
            setSubmitStatus(null);
            setIsNotificationCard(false);
          }, 3000);
          return;
        }

        await deleteUser(invitationToDelete.user_id); // Assuming user_id is the Firebase UID or similar

        // Re-fetch parents after deletion, filtered by current schoolId
        const fetchedParents = await getParents();
        const filteredParents = fetchedParents.filter((parent: { school_ids: any; role: string; }) =>
          (parent.school_ids ?? []).includes(schoolId!) && parent.role === "parent"
        );
        setParents(filteredParents);

        // Re-fetch other data if necessary
        const fetchedSchools = await getSchools();
        const fetchedStudents = await getStudents();
        setSchools(fetchedSchools);
        setStudents(fetchedStudents);

        setSubmitStatus("success");
        setNotificationMessage("Parent account deleted successfully!");
        setNotificationType("success");
        setIsNotificationCard(true);

        setTimeout(() => {
          setInvitationToDelete(null);
          setSubmitStatus(null);
          setIsNotificationCard(false);
        }, 3000);
      } catch (error) {
        console.error("Error Deleting Parent account:", error);
        setSubmitStatus("failure");
        const errorMessage =
          error instanceof Error
            ? error.message
            : "An unknown error occurred while deleting the account.";
        setNotificationMessage(errorMessage);
        setNotificationType("error");
        setIsNotificationCard(true);
      } finally {
        setIsSubmitting(false);
        setLoadingData(false);
      }
    };

    // Show loading spinner if data is being fetched or school info is not yet available
    if (loadingData || !currentSchool) { // Changed condition to ensure currentSchool is loaded
      return (
        <div className="flex justify-center items-center h-screen absolute top-0 left-0 z-50">
          <CircularLoader size={32} color="teal" />
        </div>
      );
    }

    return (
      <div className="">
        {isNotificationCard && (
          <NotificationCard
            title="Notification"
            icon={
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                <path d="M12 22C17.5 22 22 17.5 22 12C22 6.5 17.5 2 12 2C6.5 2 2 6.5 2 12C2 17.5 6.5 22 12 22Z" stroke="#15803d" strokeWidth="1.5" />
                <path d="M7.75 11.9999L10.58 14.8299L16.25 9.16992" stroke="#15803d" strokeWidth="1.5" />
              </svg>
            }
            message={notificationMessage}
            onClose={() => setIsNotificationCard(false)}
            type={notificationType}
            isVisible={isNotificationCard}
            isFixed={true}
          />
        )}
        <div className="flex justify-start mb-4">
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            transition={{ type: 'spring', stiffness: 300 }}
            onClick={() => setIsModalOpen(true)}
            className="px-4 py-2 bg-teal text-white rounded-md hover:bg-teal-600"
          >
            Add Parent
          </motion.button>
        </div>
        {isModalOpen && currentSchool && ( // Ensure currentSchool is available before opening modal
          <CreateInvitationModal
            onClose={() => { setIsModalOpen(false); setSubmitStatus(null); }}
            onSave={handleSaveInvitation}
            submitStatus={submitStatus}
            isSubmitting={isSubmitting}
            schoolId={schoolId as string} // Pass schoolId, asserted as string
            currentSchool={currentSchool} // Pass currentSchool object
          />
        )}
        {invitationToDelete && (
          <DeleteInviteModal
            className={invitationToDelete.name || ""}
            onClose={() => { setInvitationToDelete(null); setSubmitStatus(null); }}
            onDelete={handeDeleteInvitation}
            submitStatus={submitStatus}
            isSubmitting={isSubmitting}
          />
        )}
        <DataTableFix
          columns={columns}
          data={parents}
          actions={actions}
          defaultItemsPerPage={5}
          loading={loadingData}
          onLoadingChange={setLoadingData}
          showCheckbox={false}
        />
      </div>
    );
  }

  return (
    <Suspense fallback={
      <div className="flex justify-center items-center h-screen absolute top-0 left-0 z-50">
        <CircularLoader size={32} color="teal" />
      </div>
    }>
      {/* Using SchoolLayout instead of SuperLayout */}
      <SchoolLayout
        navigation={navigation}
        showGoPro={true}
        onLogout={() => console.log("Logged out")}
      >
        <ParentsComponent /> {/* Use the renamed ParentsComponent */}
      </SchoolLayout>
    </Suspense>
  );
}