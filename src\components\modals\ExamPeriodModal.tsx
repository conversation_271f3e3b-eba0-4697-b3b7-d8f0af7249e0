"use client";

import React, { useState, useEffect } from "react";
import { X, Calendar, Clock, AlertCircle, Plus, Save } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";

interface ExamPeriodModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (examPeriods: ExamPeriodData[]) => Promise<void>;
  terms: any[];
  currentAcademicYear: string;
  schoolId: string;
  loading?: boolean;
}

interface ExamPeriodData {
  term_id: string;
  name: string;
  description?: string;
  start_date: string;
  end_date: string;
  exam_type: 'mid_term' | 'final' | 'quiz' | 'test' | 'assessment' | 'other';
  academic_year: string;
  priority?: number;
  settings?: {
    allow_normal_classes?: boolean;
    suspend_normal_classes?: boolean;
    notify_teachers?: boolean;
    notify_students?: boolean;
  };
  notes?: string;
}

export default function ExamPeriodModal({
  isOpen,
  onClose,
  onSubmit,
  terms,
  currentAcademicYear,
  schoolId,
  loading = false
}: ExamPeriodModalProps) {
  const [examPeriods, setExamPeriods] = useState<ExamPeriodData[]>([]);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Initialize with one exam period per term
  useEffect(() => {
    if (isOpen && terms.length > 0) {
      const initialPeriods = terms.map(term => ({
        term_id: term._id,
        name: `${term.name} Exams`,
        description: `Examination period for ${term.name}`,
        start_date: '',
        end_date: '',
        exam_type: 'test' as const,
        academic_year: currentAcademicYear,
        priority: 100,
        settings: {
          allow_normal_classes: false,
          suspend_normal_classes: true,
          notify_teachers: true,
          notify_students: true
        },
        notes: ''
      }));
      setExamPeriods(initialPeriods);
    }
  }, [isOpen, terms, currentAcademicYear]);

  const handleInputChange = (index: number, field: keyof ExamPeriodData, value: any) => {
    const updatedPeriods = [...examPeriods];
    if (field === 'settings') {
      updatedPeriods[index].settings = { ...updatedPeriods[index].settings, ...value };
    } else {
      (updatedPeriods[index] as any)[field] = value;
    }
    setExamPeriods(updatedPeriods);
    
    // Clear error for this field
    const errorKey = `${index}.${field}`;
    if (errors[errorKey]) {
      const newErrors = { ...errors };
      delete newErrors[errorKey];
      setErrors(newErrors);
    }
  };

  const addExamPeriod = () => {
    const newPeriod: ExamPeriodData = {
      term_id: '',
      name: '',
      description: '',
      start_date: '',
      end_date: '',
      exam_type: 'test',
      academic_year: currentAcademicYear,
      priority: 100,
      settings: {
        allow_normal_classes: false,
        suspend_normal_classes: true,
        notify_teachers: true,
        notify_students: true
      },
      notes: ''
    };
    setExamPeriods([...examPeriods, newPeriod]);
  };

  const removeExamPeriod = (index: number) => {
    if (examPeriods.length > 1) {
      const updatedPeriods = examPeriods.filter((_, i) => i !== index);
      setExamPeriods(updatedPeriods);
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    examPeriods.forEach((period, index) => {
      if (!period.term_id) {
        newErrors[`${index}.term_id`] = 'Term is required';
      }
      if (!period.name.trim()) {
        newErrors[`${index}.name`] = 'Name is required';
      }
      if (!period.start_date) {
        newErrors[`${index}.start_date`] = 'Start date is required';
      }
      if (!period.end_date) {
        newErrors[`${index}.end_date`] = 'End date is required';
      }
      if (period.start_date && period.end_date && new Date(period.end_date) <= new Date(period.start_date)) {
        newErrors[`${index}.end_date`] = 'End date must be after start date';
      }
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    setIsSubmitting(true);
    try {
      await onSubmit(examPeriods);
      onClose();
    } catch (error) {
      console.error("Error creating exam periods:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const getTermName = (termId: string) => {
    const term = terms.find(t => t._id === termId);
    return term ? term.name : 'Unknown Term';
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.95 }}
        transition={{ duration: 0.2 }}
        className="bg-widget rounded-lg shadow-xl w-full max-w-4xl mx-4 max-h-[90vh] flex flex-col border border-stroke"
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-stroke">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-orange-100 dark:bg-orange-900/30 rounded-full flex items-center justify-center">
              <Calendar className="h-5 w-5 text-orange-600 dark:text-orange-400" />
            </div>
            <div>
              <h2 className="text-xl font-semibold text-foreground">Setup Exam Periods</h2>
              <p className="text-sm text-foreground/60">Configure examination periods for your school</p>
            </div>
          </div>
          <button
            onClick={onClose}
            disabled={isSubmitting}
            className="text-foreground/60 hover:text-foreground transition-colors disabled:opacity-50"
          >
            <X size={20} />
          </button>
        </div>

        {/* Info Banner */}
        <div className="p-6 bg-blue-50 dark:bg-blue-900/20 border-b border-stroke">
          <div className="flex items-start gap-3">
            <AlertCircle className="h-5 w-5 text-blue-600 dark:text-blue-400 mt-0.5 flex-shrink-0" />
            <div>
              <h3 className="font-medium text-blue-800 dark:text-blue-200 mb-1">
                Exam Mode Setup Required
              </h3>
              <p className="text-sm text-blue-700 dark:text-blue-300">
                To use exam mode, you need to define examination periods. These periods will have priority over normal classes 
                and can automatically suspend conflicting regular schedules.
              </p>
            </div>
          </div>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="flex flex-col flex-1 min-h-0">
          <div className="flex-1 overflow-y-auto modal-scrollbar p-6">
            <div className="space-y-6">
              {examPeriods.map((period, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="border border-stroke rounded-lg p-4 bg-gray-50 dark:bg-gray-800/50"
                >
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="font-medium text-foreground">
                      Exam Period {index + 1}
                      {period.term_id && (
                        <span className="text-sm text-foreground/60 ml-2">
                          ({getTermName(period.term_id)})
                        </span>
                      )}
                    </h3>
                    {examPeriods.length > 1 && (
                      <button
                        type="button"
                        onClick={() => removeExamPeriod(index)}
                        className="text-red-500 hover:text-red-700 text-sm"
                      >
                        Remove
                      </button>
                    )}
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {/* Term Selection */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Term <span className="text-red-500">*</span>
                      </label>
                      <select
                        value={period.term_id}
                        onChange={(e) => handleInputChange(index, 'term_id', e.target.value)}
                        className={`w-full px-3 py-2 border rounded-md bg-white dark:bg-gray-700 text-foreground ${
                          errors[`${index}.term_id`] ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
                        }`}
                      >
                        <option value="">Select Term</option>
                        {terms.map(term => (
                          <option key={term._id} value={term._id}>
                            {term.name} ({term.term_type})
                          </option>
                        ))}
                      </select>
                      {errors[`${index}.term_id`] && (
                        <p className="mt-1 text-sm text-red-500">{errors[`${index}.term_id`]}</p>
                      )}
                    </div>

                    {/* Exam Type */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Exam Type
                      </label>
                      <select
                        value={period.exam_type}
                        onChange={(e) => handleInputChange(index, 'exam_type', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-foreground"
                      >
                        <option value="test">Test</option>
                        <option value="mid_term">Mid-Term</option>
                        <option value="final">Final Exam</option>
                        <option value="quiz">Quiz</option>
                        <option value="assessment">Assessment</option>
                        <option value="other">Other</option>
                      </select>
                    </div>

                    {/* Name */}
                    <div className="md:col-span-2">
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Name <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        value={period.name}
                        onChange={(e) => handleInputChange(index, 'name', e.target.value)}
                        placeholder="e.g., First Term Mid-Term Exams"
                        className={`w-full px-3 py-2 border rounded-md bg-white dark:bg-gray-700 text-foreground ${
                          errors[`${index}.name`] ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
                        }`}
                      />
                      {errors[`${index}.name`] && (
                        <p className="mt-1 text-sm text-red-500">{errors[`${index}.name`]}</p>
                      )}
                    </div>

                    {/* Start Date */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Start Date <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="date"
                        value={period.start_date}
                        onChange={(e) => handleInputChange(index, 'start_date', e.target.value)}
                        className={`w-full px-3 py-2 border rounded-md bg-white dark:bg-gray-700 text-foreground ${
                          errors[`${index}.start_date`] ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
                        }`}
                      />
                      {errors[`${index}.start_date`] && (
                        <p className="mt-1 text-sm text-red-500">{errors[`${index}.start_date`]}</p>
                      )}
                    </div>

                    {/* End Date */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        End Date <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="date"
                        value={period.end_date}
                        onChange={(e) => handleInputChange(index, 'end_date', e.target.value)}
                        className={`w-full px-3 py-2 border rounded-md bg-white dark:bg-gray-700 text-foreground ${
                          errors[`${index}.end_date`] ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
                        }`}
                      />
                      {errors[`${index}.end_date`] && (
                        <p className="mt-1 text-sm text-red-500">{errors[`${index}.end_date`]}</p>
                      )}
                    </div>

                    {/* Description */}
                    <div className="md:col-span-2">
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Description
                      </label>
                      <textarea
                        value={period.description}
                        onChange={(e) => handleInputChange(index, 'description', e.target.value)}
                        placeholder="Optional description for this exam period"
                        rows={2}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-foreground"
                      />
                    </div>

                    {/* Settings */}
                    <div className="md:col-span-2">
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Settings
                      </label>
                      <div className="grid grid-cols-2 gap-3">
                        <label className="flex items-center space-x-2">
                          <input
                            type="checkbox"
                            checked={period.settings?.suspend_normal_classes || false}
                            onChange={(e) => handleInputChange(index, 'settings', { suspend_normal_classes: e.target.checked })}
                            className="rounded border-gray-300"
                          />
                          <span className="text-sm text-foreground">Suspend normal classes</span>
                        </label>
                        <label className="flex items-center space-x-2">
                          <input
                            type="checkbox"
                            checked={period.settings?.notify_teachers || false}
                            onChange={(e) => handleInputChange(index, 'settings', { notify_teachers: e.target.checked })}
                            className="rounded border-gray-300"
                          />
                          <span className="text-sm text-foreground">Notify teachers</span>
                        </label>
                      </div>
                    </div>
                  </div>
                </motion.div>
              ))}

              {/* Add More Button */}
              <button
                type="button"
                onClick={addExamPeriod}
                className="w-full py-3 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg text-foreground/60 hover:text-foreground hover:border-gray-400 transition-colors flex items-center justify-center gap-2"
              >
                <Plus className="h-4 w-4" />
                Add Another Exam Period
              </button>
            </div>
          </div>

          {/* Footer */}
          <div className="flex items-center justify-end gap-3 p-6 border-t border-stroke bg-gray-50 dark:bg-gray-800/50 flex-shrink-0">
            <button
              type="button"
              onClick={onClose}
              disabled={isSubmitting}
              className="px-4 py-2 text-foreground/70 hover:text-foreground transition-colors disabled:opacity-50"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isSubmitting || loading}
              className="px-6 py-2 bg-teal text-white rounded-md hover:bg-teal/90 transition-colors disabled:opacity-50 flex items-center gap-2"
            >
              {isSubmitting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  Creating...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4" />
                  Create Exam Periods
                </>
              )}
            </button>
          </div>
        </form>
      </motion.div>
    </div>
  );
}
