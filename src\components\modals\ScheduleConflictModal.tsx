"use client";

import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, BookO<PERSON>, Calendar, RefreshCw } from "lucide-react";
import { motion } from "framer-motion";

export interface ConflictDetails {
  teacher_name: string;
  class_name: string;
  subject_name: string;
  period_info: {
    period_number: number;
    start_time: string;
    end_time: string;
  } | null;
  day_of_week: string;
  existing_type?: string;
  current_type?: string;
  suggestion?: string;
}

export interface ScheduleConflictModalProps {
  isOpen: boolean;
  onClose: () => void;
  onReplace?: () => void;
  conflictDetails: ConflictDetails;
  message: string;
  conflictType: 'teacher' | 'period' | 'schedule';
  canReplace?: boolean;
  isExamMode?: boolean;
  loading?: boolean;
}

export default function ScheduleConflictModal({
  isOpen,
  onClose,
  onReplace,
  conflictDetails,
  message,
  conflictType,
  canReplace = false,
  isExamMode = false,
  loading = false
}: ScheduleConflictModalProps) {
  if (!isOpen) return null;

  const formatTime = (time: string) => {
    try {
      const timeParts = time.split(':');
      const hours = parseInt(timeParts[0]);
      const minutes = timeParts[1];
      
      if (hours === 0) return `12:${minutes} AM`;
      if (hours < 12) return `${hours}:${minutes} AM`;
      if (hours === 12) return `12:${minutes} PM`;
      return `${hours - 12}:${minutes} PM`;
    } catch {
      return time;
    }
  };

  const getConflictTitle = () => {
    if (conflictType === 'teacher') {
      return isExamMode ? 'Exam Supervisor Conflict' : 'Teacher Conflict';
    } else if (conflictType === 'schedule') {
      return 'Schedule Conflict';
    }
    return isExamMode ? 'Exam Period Conflict' : 'Schedule Period Conflict';
  };

  const getConflictDescription = () => {
    if (conflictType === 'teacher') {
      return 'Teacher scheduling conflict detected';
    } else if (conflictType === 'schedule') {
      return 'Time slot conflict detected';
    }
    return 'This time slot is already occupied';
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.95 }}
        transition={{ duration: 0.2 }}
        className="bg-widget rounded-lg shadow-xl w-full max-w-md mx-4 p-6 relative border border-stroke max-h-[90vh] overflow-y-auto"
      >
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center">
              <AlertTriangle className="h-5 w-5 text-red-600 dark:text-red-400" />
            </div>
            <div>
              <h2 className="text-lg font-semibold text-foreground">{getConflictTitle()}</h2>
              <p className="text-sm text-foreground/60">{getConflictDescription()}</p>
            </div>
          </div>
          <button
            onClick={onClose}
            disabled={loading}
            className="text-foreground/60 hover:text-foreground transition-colors disabled:opacity-50"
          >
            <X size={20} />
          </button>
        </div>

        {/* Alert Message */}
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6">
          <div className="flex items-start gap-3">
            <AlertTriangle className="h-5 w-5 text-red-600 dark:text-red-400 mt-0.5 flex-shrink-0" />
            <div>
              <h3 className="font-medium text-red-800 dark:text-red-200 mb-1">
                {conflictType === 'teacher'
                  ? 'Teacher Scheduling Conflict'
                  : conflictType === 'schedule'
                  ? 'Schedule Time Slot Conflict'
                  : 'Period Already Occupied'}
              </h3>
              <p className="text-sm text-red-700 dark:text-red-300">
                {message}
              </p>
            </div>
          </div>
        </div>

        {/* Conflict Details */}
        <div className="space-y-4 mb-6">
          <h3 className="font-medium text-foreground mb-3">
            {conflictType === 'teacher' ? 'Existing Assignment:' : 'Current Occupant:'}
          </h3>
          
          {/* Teacher/Supervisor */}
          <div className="flex items-center gap-3 p-3 bg-gray-50 dark:bg-gray-800/50 rounded-lg">
            <Users className="h-4 w-4 text-foreground/60" />
            <div>
              <p className="text-sm font-medium text-foreground">
                {isExamMode ? 'Supervisor' : 'Teacher'}
              </p>
              <p className="text-sm text-foreground/70">{conflictDetails.teacher_name}</p>
            </div>
          </div>

          {/* Subject */}
          <div className="flex items-center gap-3 p-3 bg-gray-50 dark:bg-gray-800/50 rounded-lg">
            <BookOpen className="h-4 w-4 text-foreground/60" />
            <div>
              <p className="text-sm font-medium text-foreground">Subject</p>
              <p className="text-sm text-foreground/70">{conflictDetails.subject_name}</p>
            </div>
          </div>

          {/* Class */}
          <div className="flex items-center gap-3 p-3 bg-gray-50 dark:bg-gray-800/50 rounded-lg">
            <Users className="h-4 w-4 text-foreground/60" />
            <div>
              <p className="text-sm font-medium text-foreground">Class</p>
              <p className="text-sm text-foreground/70">{conflictDetails.class_name}</p>
            </div>
          </div>

          {/* Time & Day */}
          <div className="flex items-center gap-3 p-3 bg-gray-50 dark:bg-gray-800/50 rounded-lg">
            <Clock className="h-4 w-4 text-foreground/60" />
            <div>
              <p className="text-sm font-medium text-foreground">Time Conflict</p>
              <p className="text-sm text-foreground/70">
                {conflictDetails.day_of_week}
                {conflictDetails.period_info && (
                  <span>
                    {" "}• Period {conflictDetails.period_info.period_number} 
                    ({formatTime(conflictDetails.period_info.start_time)} - {formatTime(conflictDetails.period_info.end_time)})
                  </span>
                )}
              </p>
            </div>
          </div>

          {/* Schedule Type Conflict (only for schedule conflicts) */}
          {conflictType === 'schedule' && conflictDetails.existing_type && conflictDetails.current_type && (
            <div className="flex items-center gap-3 p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border border-yellow-200 dark:border-yellow-800">
              <Calendar className="h-4 w-4 text-yellow-600 dark:text-yellow-400" />
              <div>
                <p className="text-sm font-medium text-yellow-800 dark:text-yellow-200">Schedule Type Conflict</p>
                <p className="text-sm text-yellow-700 dark:text-yellow-300">
                  Existing: <span className="font-medium">{conflictDetails.existing_type}</span> •
                  Attempting: <span className="font-medium">{conflictDetails.current_type}</span>
                </p>
                {conflictDetails.suggestion && (
                  <p className="text-xs text-yellow-600 dark:text-yellow-400 mt-1">
                    💡 {conflictDetails.suggestion}
                  </p>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Replacement Option */}
        {canReplace && (
          <div className="mb-6 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
            <div className="flex items-start gap-3">
              <RefreshCw className="h-5 w-5 text-blue-600 dark:text-blue-400 mt-0.5 flex-shrink-0" />
              <div>
                <h4 className="font-medium text-blue-800 dark:text-blue-200 mb-2">
                  Replace Existing Entry?
                </h4>
                <p className="text-sm text-blue-700 dark:text-blue-300 mb-3">
                  You can replace the existing {isExamMode ? 'exam' : 'schedule'} entry with your new one. 
                  This will remove the current assignment and create your new entry.
                </p>
                <p className="text-xs text-blue-600 dark:text-blue-400">
                  <strong>Note:</strong> This action cannot be undone. The existing entry will be permanently removed.
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Actions */}
        <div className="flex justify-end space-x-3">
          <button
            onClick={onClose}
            disabled={loading}
            className="px-4 py-2 text-sm font-medium text-foreground/70 bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Cancel
          </button>
          {canReplace && onReplace && (
            <button
              onClick={onReplace}
              disabled={loading}
              className="px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
            >
              {loading ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  <span>Replacing...</span>
                </>
              ) : (
                <>
                  <RefreshCw className="h-4 w-4" />
                  <span>Replace Entry</span>
                </>
              )}
            </button>
          )}
        </div>
      </motion.div>
    </div>
  );
}
