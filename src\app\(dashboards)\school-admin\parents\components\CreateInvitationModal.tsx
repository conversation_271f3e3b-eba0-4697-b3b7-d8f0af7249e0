"use client";

import React, { useRef, useState, useEffect } from "react";
import { X, ChevronDown } from "lucide-react";
// Adjusted relative paths for components and services
import CustomInput from '../../../../../components/inputs/CustomInput';
import CustomPhoneInput from '../../../../../components/inputs/CustomPhoneInput';
import { getSchools } from '../../../../services/SchoolServices'; // Still needed for type SchoolSchema (not used for selection)
import { getStudents } from '../../../../services/StudentServices';
import { StudentSchema } from '../../../../models/StudentModel';
import { SchoolSchema } from '../../../../models/SchoolModel';
import SubmissionFeedback from '../../../../../components/widgets/SubmissionFeedback';
import CircularLoader from '../../../../../components/widgets/CircularLoader';
import { motion } from "framer-motion";
import { UserSchema } from '../../../../models/UserModel';

interface CreateInvitationModalProps {
  onClose: () => void;
  onSave: (invitationData: UserSchema) => Promise<void>;
  submitStatus: "success" | "failure" | null;
  isSubmitting: boolean;
  schoolId: string; // The ID of the current school
  currentSchool: SchoolSchema; // The details of the current school
}

const CreateInvitationModal: React.FC<CreateInvitationModalProps> = ({
  onClose,
  onSave,
  submitStatus,
  isSubmitting,
  schoolId, 
  currentSchool,
}) => {
  const [formData, setFormData] = useState<UserSchema>({
    _id: "",
    user_id: "",
    role: "parent",
    email: "",
    phone: "",
    name: "",
    school_ids: [schoolId], // Initialize with the provided schoolId
    student_ids: [] as string[],
  });

  const [countryCode, setCountryCode] = useState("+237");
  const [allChildren, setAllChildren] = useState<StudentSchema[]>([]);
  const [filteredChildren, setFilteredChildren] = useState<StudentSchema[]>([]); // Students filtered by current school
  const [searchTerm, setSearchTerm] = useState("");
  const [showChildrenDropdown, setShowChildrenDropdown] = useState(false);

  const childrenDropdownRef = useRef<HTMLDivElement>(null);

  // Fetch all students (they will be filtered later by the current school)
  useEffect(() => {
    getStudents()
      .then((data) => {
        const studentsWithId = data.map((student: any) => ({
          ...student,
          id: student.id || student._id, // Ensure student has an 'id' property
        }));
        setAllChildren(studentsWithId); // Store all students fetched
      })
      .catch((error) => console.error("Error fetching children:", error));
  }, []);

  // Filter students based on the current schoolId whenever allChildren or schoolId changes
  useEffect(() => {
    const studentsForCurrentSchool = allChildren.filter(
      (child) => child.school_id === schoolId
    );
    setFilteredChildren(studentsForCurrentSchool); // Set students visible in the dropdown
  }, [allChildren, schoolId]); // Depend on allChildren and schoolId

  // Close children dropdown on outside click
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        childrenDropdownRef.current &&
        !childrenDropdownRef.current.contains(event.target as Node)
      ) {
        setShowChildrenDropdown(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const fullPhone = `${countryCode}${(formData.phone ?? "").replace(/^0+/, "")}`;
    onSave({
      ...formData,
      phone: fullPhone,
      // Ensure school_ids always contains only the current schoolId when saving
      school_ids: [schoolId], 
    });
  };

  const toggleChildSelection = (childId: string, isChecked: boolean) => {
    setFormData((prev) => {
      const updatedChildren = isChecked
        ? [...(prev.student_ids ?? []), childId]
        : (prev.student_ids ?? []).filter((id) => id !== childId);
      return {
        ...prev,
        // Using Array.from(new Set(...)) to ensure uniqueness, though filter/add should handle it
        student_ids: Array.from(new Set(updatedChildren)), 
      };
    });
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="h-[550px] bg-white dark:bg-gray-800 rounded-lg shadow-lg w-full max-w-4xl mx-4 sm:mx-6 md:mx-0 relative flex flex-col md:flex-row">
        {/* Left Image */}
        <div className="hidden md:block md:w-1/2 h-full">
          <img
            src="/assets/images/parent1.png"
            alt="Parent Invite"
            className="w-full h-full object-cover rounded-lg"
            draggable={false}
          />
        </div>

        {/* Form Section */}
        <div className="w-full md:w-1/2 p-6 overflow-y-auto custom-scrollbar">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-lg font-semibold text-foreground">Send Invitation</h2>
            <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
              <X size={20} />
            </button>
          </div>
          {submitStatus ? (
            <SubmissionFeedback status={submitStatus}
              message={
                submitStatus === "success"
                  ? "Invitation has been sent Successfully!"
                  : "There was an error sending this invitation. Try again and if this persist contact support!"
              } />
          ) : (
            <form onSubmit={handleSubmit}>
              <CustomInput
                label="Full Name"
                id="name"
                name="name"
                value={formData.name}
                onChange={handleChange}
                required
              />

              <CustomInput
                label="Email"
                id="email"
                name="email"
                value={formData.email}
                onChange={handleChange}
                required
              />

              <CustomPhoneInput
                label="Phone Number"
                id="phone"
                name="phone"
                value={formData.phone ?? ""}
                onChange={handleChange}
                countryCode={countryCode}
                onCountryCodeChange={(e) => setCountryCode(e.target.value)}
                required 
                countryCodeName={""} // Assuming this prop is not directly used for display or dynamic name generation
              />
              <CustomInput
                label="Address"
                id="address"
                name="address"
                value={formData.address ?? ""}
                onChange={handleChange}
                required
              />

              {/* School Display (Read-only) */}
              <div className="mb-4 flex flex-col relative">
                <label className="text-sm font-semibold mb-1">School</label>
                <div className="w-full px-3 py-2 border rounded-md text-sm dark:bg-gray-700 bg-gray-100 dark:text-gray-300 cursor-not-allowed">
                  {currentSchool ? currentSchool.name : "Loading school..."}
                </div>
              </div>

              {/* Children Dropdown (filtered by current school) */}
              <div className="mb-4 flex flex-col relative">
                <label className="text-sm font-semibold mb-1">Children</label>
                <div
                  onClick={() => setShowChildrenDropdown((prev) => !prev)}
                  className="w-full px-3 py-2 border rounded-md text-sm dark:bg-gray-700 bg-white dark:text-white cursor-pointer flex items-center justify-between"
                >
                  <span>
                    {(formData.student_ids ?? []).length > 0
                      ? filteredChildren // Use filteredChildren for display
                          .filter((child) =>
                            (formData.student_ids ?? []).includes(child.id as string)
                          )
                          .map((child) => child.name)
                          .join(", ")
                      : "Select children"}
                  </span>
                  <ChevronDown className="w-4 h-4 text-gray-500" />
                </div>

                {showChildrenDropdown && (
                  <div
                    ref={childrenDropdownRef}
                    className="absolute z-10 bg-white dark:bg-gray-700 mt-1 rounded-md border max-h-48 overflow-y-auto p-2 shadow-lg w-[calc(100%-2px)] left-0 top-full"
                  >
                    <input
                      type="text"
                      placeholder="Search children..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="w-full mb-2 px-2 py-1 border rounded-md text-sm dark:bg-gray-600"
                    />
                    {filteredChildren // Display only children for the current school
                      .filter((child) =>
                        child.name.toLowerCase().includes(searchTerm.toLowerCase())
                      )
                      .map((child) => (
                        <label
                          key={child.id as string}
                          className="flex items-center gap-2 px-2 py-1 text-sm"
                        >
                          <input
                            type="checkbox"
                            checked={(formData.student_ids ?? []).includes(child.id as string)}
                            onChange={(e) =>
                              toggleChildSelection(child.id as string, e.target.checked)
                            }
                          />
                          {child.name}
                        </label>
                      ))}
                    {filteredChildren.length === 0 && (
                        <p className="text-sm text-gray-500 p-2">No students found for this school.</p>
                    )}
                  </div>
                )}
              </div>

              <div className="flex justify-end space-x-2 mt-6">
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  transition={{ type: 'spring', stiffness: 300 }}
                  type="button"
                  onClick={onClose}
                  className="px-4 py-2 bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-300 dark:hover:bg-gray-500"
                  disabled={isSubmitting}
                >
                  Cancel
                </motion.button>
                
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  transition={{ type: 'spring', stiffness: 300 }}
                  type="submit"
                  className="px-4 py-2 bg-teal text-white rounded-md hover:bg-teal-600 flex items-center gap-2"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? (
                    <>
                      <CircularLoader size={18} color="teal-500" />
                      Sending...
                    </>
                  ) : (
                    "Send Invitation"
                  )}
                </motion.button>
              </div>
            </form>
          )}
        </div>
      </div>
    </div>
  );
};

export default CreateInvitationModal;
