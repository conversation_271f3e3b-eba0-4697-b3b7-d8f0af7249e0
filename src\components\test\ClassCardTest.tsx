"use client";

import React from "react";
import ClassCard from "@/components/Dashboard/ClassCard";
import { ClassStats, ClassData } from "@/app/services/ClassStatisticsServices";

// Test data
const testClassData: ClassData = {
  _id: "test-class-1",
  name: "Form 4A",
  grade_level: "4",
  student_count: 30,
  section: "A"
};

const testStats: ClassStats = {
  totalSlots: 35,
  occupiedSlots: 28,
  freeSlots: 7,
  assignedTeachers: 8,
  totalSubjects: 12,
  examSlots: 5
};

export default function ClassCardTest() {
  const handleClick = () => {
    console.log("Class card clicked!");
  };

  return (
    <div className="p-8 bg-gray-100 dark:bg-gray-900 min-h-screen">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-2xl font-bold text-foreground mb-8">ClassCard Test</h1>
        
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* Normal Mode */}
          <ClassCard
            classData={testClassData}
            stats={testStats}
            isExamMode={false}
            onClick={handleClick}
            index={0}
          />
          
          {/* Exam Mode */}
          <ClassCard
            classData={{
              ...testClassData,
              _id: "test-class-2",
              name: "Form 5B",
              grade_level: "5",
              section: "B",
              student_count: 25
            }}
            stats={{
              ...testStats,
              occupiedSlots: 15,
              freeSlots: 20,
              assignedTeachers: 6,
              examSlots: 8
            }}
            isExamMode={true}
            onClick={handleClick}
            index={1}
          />
          
          {/* Low Coverage */}
          <ClassCard
            classData={{
              ...testClassData,
              _id: "test-class-3",
              name: "Form 6C",
              grade_level: "6",
              section: "C",
              student_count: 20
            }}
            stats={{
              ...testStats,
              occupiedSlots: 10,
              freeSlots: 25,
              assignedTeachers: 3,
              totalSubjects: 8
            }}
            isExamMode={false}
            onClick={handleClick}
            index={2}
          />
        </div>
      </div>
    </div>
  );
}
