import { getTokenFromCookie } from "./UserServices";

const BASE_API_URL = "http://localhost:3002/api"; // For local development, change as needed

export interface ClassStats {
  totalSlots: number;
  occupiedSlots: number;
  freeSlots: number;
  assignedTeachers: number;
  totalSubjects: number;
  examSlots?: number;
}

export interface ClassData {
  _id: string;
  name: string;
  grade_level?: string;
  student_count?: number;
  section?: string;
}

export interface ClassStatistic {
  classData: ClassData;
  stats: ClassStats;
}

export interface ClassStatisticsResponse {
  classStatistics: ClassStatistic[];
  totalClasses: number;
  message: string;
}

// Get class statistics for a school
export async function getClassStatistics(
  schoolId: string, 
  filters: {
    academic_year?: string;
    session_year?: string;
    schedule_type?: string;
  } = {}
): Promise<ClassStatisticsResponse> {
  const token = getTokenFromCookie("idToken");

  try {
    const queryParams = new URLSearchParams();
    if (filters.academic_year) queryParams.append('academic_year', filters.academic_year);
    if (filters.session_year) queryParams.append('session_year', filters.session_year);
    if (filters.schedule_type) queryParams.append('schedule_type', filters.schedule_type);

    const queryString = queryParams.toString();
    const url = `${BASE_API_URL}/timetable/school/${schoolId}/class-statistics${queryString ? `?${queryString}` : ''}`;

    const response = await fetch(url, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      console.error("Error fetching class statistics:", response.statusText);
      throw new Error("Failed to fetch class statistics");
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Fetch class statistics error:", error);
    throw new Error("Failed to fetch class statistics");
  }
}

// Calculate occupancy percentage
export function calculateOccupancyPercentage(stats: ClassStats): number {
  return stats.totalSlots > 0 
    ? Math.round((stats.occupiedSlots / stats.totalSlots) * 100) 
    : 0;
}

// Get occupancy color based on percentage
export function getOccupancyColor(percentage: number): string {
  if (percentage >= 80) return "text-green-600 dark:text-green-400";
  if (percentage >= 50) return "text-yellow-600 dark:text-yellow-400";
  return "text-red-600 dark:text-red-400";
}

// Get occupancy background color based on percentage
export function getOccupancyBgColor(percentage: number): string {
  if (percentage >= 80) return "bg-green-100 dark:bg-green-900/20";
  if (percentage >= 50) return "bg-yellow-100 dark:bg-yellow-900/20";
  return "bg-red-100 dark:bg-red-900/20";
}

// Get occupancy status text
export function getOccupancyStatus(percentage: number): string {
  if (percentage >= 80) return "High Coverage";
  if (percentage >= 50) return "Medium Coverage";
  return "Low Coverage";
}

// Format class display name
export function formatClassName(classData: ClassData): string {
  let name = classData.name;
  if (classData.section) {
    name += ` (Section ${classData.section})`;
  }
  return name;
}

// Get class grade display
export function getClassGradeDisplay(classData: ClassData): string {
  if (classData.grade_level) {
    return `Grade ${classData.grade_level}`;
  }
  return "No Grade";
}

// Get student count display
export function getStudentCountDisplay(classData: ClassData): string {
  if (classData.student_count) {
    return `${classData.student_count} student${classData.student_count !== 1 ? 's' : ''}`;
  }
  return "No students";
}

// Sort classes by various criteria
export function sortClassStatistics(
  classStats: ClassStatistic[], 
  sortBy: 'name' | 'occupancy' | 'teachers' | 'students' = 'name',
  order: 'asc' | 'desc' = 'asc'
): ClassStatistic[] {
  return [...classStats].sort((a, b) => {
    let valueA: number | string;
    let valueB: number | string;

    switch (sortBy) {
      case 'name':
        valueA = a.classData.name.toLowerCase();
        valueB = b.classData.name.toLowerCase();
        break;
      case 'occupancy':
        valueA = calculateOccupancyPercentage(a.stats);
        valueB = calculateOccupancyPercentage(b.stats);
        break;
      case 'teachers':
        valueA = a.stats.assignedTeachers;
        valueB = b.stats.assignedTeachers;
        break;
      case 'students':
        valueA = a.classData.student_count || 0;
        valueB = b.classData.student_count || 0;
        break;
      default:
        valueA = a.classData.name.toLowerCase();
        valueB = b.classData.name.toLowerCase();
    }

    if (typeof valueA === 'string' && typeof valueB === 'string') {
      return order === 'asc' 
        ? valueA.localeCompare(valueB)
        : valueB.localeCompare(valueA);
    }

    const numA = valueA as number;
    const numB = valueB as number;
    return order === 'asc' ? numA - numB : numB - numA;
  });
}

// Filter classes by search term
export function filterClassStatistics(
  classStats: ClassStatistic[], 
  searchTerm: string
): ClassStatistic[] {
  if (!searchTerm.trim()) return classStats;

  const term = searchTerm.toLowerCase();
  return classStats.filter(stat => 
    stat.classData.name.toLowerCase().includes(term) ||
    stat.classData.grade_level?.toLowerCase().includes(term) ||
    stat.classData.section?.toLowerCase().includes(term)
  );
}

// Get summary statistics
export function getSummaryStatistics(classStats: ClassStatistic[]): {
  totalClasses: number;
  totalSlots: number;
  totalOccupiedSlots: number;
  totalFreeSlots: number;
  averageOccupancy: number;
  totalTeachers: number;
  totalSubjects: number;
} {
  const totalClasses = classStats.length;
  const totalSlots = classStats.reduce((sum, stat) => sum + stat.stats.totalSlots, 0);
  const totalOccupiedSlots = classStats.reduce((sum, stat) => sum + stat.stats.occupiedSlots, 0);
  const totalFreeSlots = classStats.reduce((sum, stat) => sum + stat.stats.freeSlots, 0);
  const averageOccupancy = totalSlots > 0 ? Math.round((totalOccupiedSlots / totalSlots) * 100) : 0;
  
  // Get unique teachers and subjects across all classes
  const allTeachers = new Set<number>();
  const allSubjects = new Set<number>();
  
  classStats.forEach(stat => {
    allTeachers.add(stat.stats.assignedTeachers);
    allSubjects.add(stat.stats.totalSubjects);
  });

  return {
    totalClasses,
    totalSlots,
    totalOccupiedSlots,
    totalFreeSlots,
    averageOccupancy,
    totalTeachers: Array.from(allTeachers).reduce((sum, count) => sum + count, 0),
    totalSubjects: Array.from(allSubjects).reduce((sum, count) => sum + count, 0)
  };
}
